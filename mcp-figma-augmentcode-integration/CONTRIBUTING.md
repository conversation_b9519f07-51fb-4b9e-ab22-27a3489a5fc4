# Contributing to mcp-figma-augmentcode-integration

Thank you for your interest in contributing! 🚀

## How to contribute

1. Fork this repository
2. Create a feature branch (`git checkout -b feature/your-feature`)
3. Commit your changes (`git commit -m 'Add your feature'`)
4. Push to the branch (`git push origin feature/your-feature`)
5. Open a pull request

## How to report issues

- Check open issues to see if it’s already reported
- If not, [open a new issue](https://github.com/shedytee/mcp-figma-augmentcode-integration/issues) with:
  - What you tried
  - Error messages or screenshots
  - Steps to reproduce

## Code style

- Follow the existing code style
- Add comments where useful
- Keep commits focused and descriptive

Thanks for helping improve the project! 💛
