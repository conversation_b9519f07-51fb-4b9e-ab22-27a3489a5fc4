{"name": "mcp-figma-augmentcode-integration", "version": "1.0.0", "description": "Figma MCP Server for Augment Code Integration", "main": "figma-mcp-server.js", "type": "module", "scripts": {"start:mcp": "node figma-mcp-server.js", "start": "node figma-mcp-server.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "figma-js": "^1.16.0", "zod": "^3.22.0", "dotenv": "^16.3.0"}, "keywords": ["mcp", "figma", "augment-code", "model-context-protocol"], "author": "", "license": "MIT"}