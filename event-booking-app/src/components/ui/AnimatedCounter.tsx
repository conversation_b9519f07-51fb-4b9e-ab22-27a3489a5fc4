import React, { useEffect, useState } from 'react';
import { motion, useAnimation } from 'framer-motion';

interface AnimatedCounterProps {
  from: number;
  to: number;
  duration?: number;
  className?: string;
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  from,
  to,
  duration = 2,
  className
}) => {
  const [count, setCount] = useState(from);
  const controls = useAnimation();

  useEffect(() => {
    const startAnimation = async () => {
      await controls.start({
        opacity: 1,
        transition: { duration: 0.5 }
      });

      const increment = (to - from) / (duration * 60); // 60fps
      let current = from;
      
      const timer = setInterval(() => {
        current += increment;
        if (current >= to) {
          setCount(to);
          clearInterval(timer);
        } else {
          setCount(Math.floor(current));
        }
      }, 1000 / 60);

      return () => clearInterval(timer);
    };

    startAnimation();
  }, [from, to, duration, controls]);

  return (
    <motion.span
      className={className}
      initial={{ opacity: 0 }}
      animate={controls}
    >
      {count.toLocaleString()}
    </motion.span>
  );
};

export default AnimatedCounter;
