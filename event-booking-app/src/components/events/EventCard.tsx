import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, MapPin, Clock, Tag } from 'lucide-react';
import type { Event } from '../../types';
import { formatDate, formatPrice } from '../../utils/formatters';
import { cn } from '../../utils/cn';

interface EventCardProps {
  event: Event;
  variant?: 'default' | 'featured' | 'compact';
  className?: string;
}

const EventCard: React.FC<EventCardProps> = ({ 
  event, 
  variant = 'default',
  className 
}) => {
  const {
    id,
    title,
    description,
    image,
    date,
    time,
    venue,
    category,
    ticketPrice,
    soldOut
  } = event;

  const cardVariants = {
    default: 'flex flex-col h-full',
    featured: 'flex flex-col md:flex-row h-full',
    compact: 'flex flex-row h-full'
  };

  const imageVariants = {
    default: 'aspect-[16/9] w-full rounded-t-lg overflow-hidden',
    featured: 'aspect-[16/9] w-full md:w-2/5 rounded-t-lg md:rounded-l-lg md:rounded-tr-none overflow-hidden',
    compact: 'aspect-square w-24 h-24 rounded-lg overflow-hidden flex-shrink-0'
  };

  const contentVariants = {
    default: 'flex flex-col flex-1 p-4',
    featured: 'flex flex-col flex-1 p-4 md:p-6',
    compact: 'flex flex-col flex-1 p-3'
  };

  return (
    <motion.div
      className={cn(
        'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden transition-all duration-200 hover:shadow-md',
        cardVariants[variant],
        className
      )}
      whileHover={{ y: -4 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      {/* Image */}
      <div className={imageVariants[variant]}>
        <img 
          src={image} 
          alt={title} 
          className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
        />
        {soldOut && (
          <div className="absolute top-2 right-2 bg-red-600 text-white text-xs font-bold px-2 py-1 rounded">
            SOLD OUT
          </div>
        )}
        {event.featured && variant !== 'featured' && (
          <div className="absolute top-2 left-2 bg-primary-600 text-white text-xs font-bold px-2 py-1 rounded">
            FEATURED
          </div>
        )}
      </div>

      {/* Content */}
      <div className={contentVariants[variant]}>
        {/* Category */}
        <div className="flex items-center mb-2">
          <span className="text-xs font-medium px-2 py-1 bg-primary-100 text-primary-800 rounded-full capitalize">
            {category}
          </span>
        </div>

        {/* Title */}
        <h3 className={cn(
          "font-heading font-semibold text-gray-900 mb-2",
          variant === 'compact' ? 'text-sm' : 'text-lg'
        )}>
          {title}
        </h3>

        {/* Description - Only for default and featured */}
        {variant !== 'compact' && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {description}
          </p>
        )}

        {/* Event Details */}
        <div className="mt-auto space-y-2">
          {/* Date & Time */}
          <div className="flex items-center text-gray-600">
            <Calendar className="w-4 h-4 mr-2 flex-shrink-0" />
            <span className="text-sm">{formatDate(date)}</span>
            {variant !== 'compact' && (
              <>
                <Clock className="w-4 h-4 ml-3 mr-2 flex-shrink-0" />
                <span className="text-sm">{time}</span>
              </>
            )}
          </div>

          {/* Location */}
          <div className="flex items-center text-gray-600">
            <MapPin className="w-4 h-4 mr-2 flex-shrink-0" />
            <span className="text-sm truncate">
              {venue.name}, {venue.city}
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center justify-between mt-3">
            <div className="flex items-center text-gray-600">
              <Tag className="w-4 h-4 mr-2 flex-shrink-0" />
              <span className="text-sm font-medium">
                {formatPrice(ticketPrice.min, ticketPrice.currency)}
                {ticketPrice.min !== ticketPrice.max && '+'} 
              </span>
            </div>
            
            {variant !== 'compact' && (
              <motion.a
                href={`/events/${id}`}
                className="text-sm font-medium text-primary-600 hover:text-primary-700"
                whileHover={{ x: 3 }}
              >
                View Details →
              </motion.a>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default EventCard;
