import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Filter, X, Calendar, MapPin, DollarSign } from 'lucide-react';
import type { EventFilter, EventCategory } from '../../types';
import Button from '../ui/Button';
import { cn } from '../../utils/cn';
import { useResponsive } from '../../hooks/useResponsive';

interface EventFiltersProps {
  filters: EventFilter;
  onFiltersChange: (filters: EventFilter) => void;
  className?: string;
}

const EventFilters: React.FC<EventFiltersProps> = ({
  filters,
  onFiltersChange,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { isMobile } = useResponsive();

  const categories: { value: EventCategory; label: string }[] = [
    { value: 'music', label: 'Music' },
    { value: 'comedy', label: 'Comedy' },
    { value: 'food', label: 'Food' },
    { value: 'art', label: 'Art' },
    { value: 'sports', label: 'Sports' },
    { value: 'theater', label: 'Theater' },
    { value: 'workshop', label: 'Workshop' },
    { value: 'conference', label: 'Conference' },
  ];

  const handleCategoryChange = (category: EventCategory) => {
    onFiltersChange({
      ...filters,
      category: filters.category === category ? undefined : category
    });
  };

  const handlePriceRangeChange = (min: number, max: number) => {
    onFiltersChange({
      ...filters,
      priceRange: { min, max }
    });
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  const hasActiveFilters = Object.keys(filters).some(key => 
    filters[key as keyof EventFilter] !== undefined
  );

  return (
    <div className={cn("relative", className)}>
      {/* Mobile Filter Toggle */}
      <div className="md:hidden mb-4">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full justify-between"
        >
          <div className="flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Filters
            {hasActiveFilters && (
              <span className="ml-2 bg-primary-600 text-white text-xs px-2 py-1 rounded-full">
                Active
              </span>
            )}
          </div>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <X className={cn("w-4 h-4", isOpen ? "block" : "hidden")} />
            <Filter className={cn("w-4 h-4", isOpen ? "hidden" : "block")} />
          </motion.div>
        </Button>
      </div>

      {/* Filter Panel */}
      <AnimatePresence>
        {(isOpen || !isMobile) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white rounded-lg border border-gray-200 p-6 space-y-6"
          >
            {/* Clear Filters */}
            {hasActiveFilters && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">
                  Active Filters
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-primary-600 hover:text-primary-700"
                >
                  Clear All
                </Button>
              </div>
            )}

            {/* Categories */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <Filter className="w-4 h-4 mr-2" />
                Categories
              </h3>
              <div className="grid grid-cols-2 gap-2">
                {categories.map((category) => (
                  <motion.button
                    key={category.value}
                    onClick={() => handleCategoryChange(category.value)}
                    className={cn(
                      "px-3 py-2 text-sm rounded-lg border transition-colors text-left",
                      filters.category === category.value
                        ? "bg-primary-50 border-primary-200 text-primary-700"
                        : "bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
                    )}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {category.label}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Price Range */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <DollarSign className="w-4 h-4 mr-2" />
                Price Range
              </h3>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { label: 'Free', min: 0, max: 0 },
                  { label: 'Under ₹500', min: 0, max: 500 },
                  { label: '₹500 - ₹1000', min: 500, max: 1000 },
                  { label: '₹1000 - ₹2500', min: 1000, max: 2500 },
                  { label: '₹2500+', min: 2500, max: 10000 },
                ].map((range) => (
                  <motion.button
                    key={range.label}
                    onClick={() => handlePriceRangeChange(range.min, range.max)}
                    className={cn(
                      "px-3 py-2 text-sm rounded-lg border transition-colors text-left",
                      filters.priceRange?.min === range.min && filters.priceRange?.max === range.max
                        ? "bg-primary-50 border-primary-200 text-primary-700"
                        : "bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
                    )}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {range.label}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Location */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <MapPin className="w-4 h-4 mr-2" />
                Location
              </h3>
              <input
                type="text"
                placeholder="Enter city or venue"
                value={filters.location || ''}
                onChange={(e) => onFiltersChange({ ...filters, location: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            {/* Date Range */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                Date Range
              </h3>
              <div className="grid grid-cols-2 gap-3">
                <input
                  type="date"
                  value={filters.dateRange?.start || ''}
                  onChange={(e) => onFiltersChange({
                    ...filters,
                    dateRange: { ...filters.dateRange, start: e.target.value, end: filters.dateRange?.end || '' }
                  })}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                <input
                  type="date"
                  value={filters.dateRange?.end || ''}
                  onChange={(e) => onFiltersChange({
                    ...filters,
                    dateRange: { start: filters.dateRange?.start || '', end: e.target.value }
                  })}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EventFilters;
