import React from 'react';
import { motion } from 'framer-motion';
import type { Event } from '../../types';
import EventCard from './EventCard';
import { cn } from '../../utils/cn';

interface EventGridProps {
  events: Event[];
  variant?: 'default' | 'featured' | 'compact';
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

const EventGrid: React.FC<EventGridProps> = ({ 
  events, 
  variant = 'default',
  columns = 3,
  className 
}) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  if (events.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
        <p className="text-gray-600">Try adjusting your search criteria or check back later for new events.</p>
      </div>
    );
  }

  return (
    <motion.div
      className={cn(
        'grid gap-6',
        gridClasses[columns],
        className
      )}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {events.map((event) => (
        <motion.div key={event.id} variants={itemVariants}>
          <EventCard event={event} variant={variant} />
        </motion.div>
      ))}
    </motion.div>
  );
};

export default EventGrid;
