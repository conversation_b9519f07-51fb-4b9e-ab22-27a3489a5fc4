import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Search, Grid, List } from 'lucide-react';
import type { EventFilter } from '../types';
import { sampleEvents } from '../data/sampleEvents';
import EventGrid from '../components/events/EventGrid';
import EventFilters from '../components/events/EventFilters';
import Button from '../components/ui/Button';
import { cn } from '../utils/cn';

const Events: React.FC = () => {
  const [filters, setFilters] = useState<EventFilter>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'date' | 'price' | 'popularity'>('date');

  // Filter and search events
  const filteredEvents = useMemo(() => {
    let filtered = [...sampleEvents];

    // Apply search query
    if (searchQuery) {
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.venue.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(event => event.category === filters.category);
    }

    // Apply price range filter
    if (filters.priceRange) {
      filtered = filtered.filter(event =>
        event.ticketPrice.min >= filters.priceRange!.min &&
        event.ticketPrice.max <= filters.priceRange!.max
      );
    }

    // Apply location filter
    if (filters.location) {
      filtered = filtered.filter(event =>
        event.venue.city.toLowerCase().includes(filters.location!.toLowerCase()) ||
        event.venue.name.toLowerCase().includes(filters.location!.toLowerCase())
      );
    }

    // Apply date range filter
    if (filters.dateRange?.start) {
      filtered = filtered.filter(event => event.date >= filters.dateRange!.start);
    }
    if (filters.dateRange?.end) {
      filtered = filtered.filter(event => event.date <= filters.dateRange!.end);
    }

    // Sort events
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(a.date).getTime() - new Date(b.date).getTime();
        case 'price':
          return a.ticketPrice.min - b.ticketPrice.min;
        case 'popularity':
          return b.featured ? 1 : -1;
        default:
          return 0;
      }
    });

    return filtered;
  }, [sampleEvents, filters, searchQuery, sortBy]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl md:text-4xl font-heading font-bold text-gray-900 mb-4">
              Discover Events
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Find amazing events happening in your city and beyond.
            </p>

            {/* Search Bar */}
            <div className="relative max-w-2xl">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search events, artists, venues..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-80 flex-shrink-0">
            <EventFilters
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Controls */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  {filteredEvents.length} events found
                </span>
                
                {/* Sort Dropdown */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'date' | 'price' | 'popularity')}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                >
                  <option value="date">Sort by Date</option>
                  <option value="price">Sort by Price</option>
                  <option value="popularity">Sort by Popularity</option>
                </select>
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'grid' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="p-2"
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="p-2"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Events Grid/List */}
            <motion.div
              key={viewMode}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <EventGrid
                events={filteredEvents}
                variant={viewMode === 'list' ? 'compact' : 'default'}
                columns={viewMode === 'list' ? 1 : 3}
                className={cn(
                  viewMode === 'list' && "space-y-4"
                )}
              />
            </motion.div>

            {/* Load More Button */}
            {filteredEvents.length > 0 && (
              <div className="text-center mt-12">
                <Button variant="outline" size="lg">
                  Load More Events
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Events;
