{"version": 3, "file": "read-entry.js", "sourceRoot": "", "sources": ["../../src/read-entry.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAA;AAEnC,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAIlE,MAAM,OAAO,SAAU,SAAQ,QAAwB;IACrD,QAAQ,CAAM;IACd,cAAc,CAAM;IACpB,MAAM,CAAQ;IACd,cAAc,CAAQ;IACtB,WAAW,CAAQ;IACnB,MAAM,CAAQ;IACd,IAAI,CAAe;IACnB,IAAI,GAAY,KAAK,CAAA;IACrB,MAAM,GAAY,KAAK,CAAA;IACvB,IAAI,CAAQ;IACZ,IAAI,CAAS;IACb,GAAG,CAAS;IACZ,GAAG,CAAS;IACZ,KAAK,CAAS;IACd,KAAK,CAAS;IACd,IAAI,GAAW,CAAC,CAAA;IAChB,KAAK,CAAO;IACZ,KAAK,CAAO;IACZ,KAAK,CAAO;IACZ,QAAQ,CAAS;IAEjB,GAAG,CAAS;IACZ,GAAG,CAAS;IACZ,KAAK,CAAS;IACd,OAAO,GAAY,KAAK,CAAA;IACxB,QAAQ,CAAS;IACjB,WAAW,GAAY,KAAK,CAAA;IAE5B,YAAY,MAAc,EAAE,EAAQ,EAAE,GAAS;QAC7C,KAAK,CAAC,EAAE,CAAC,CAAA;QACT,+DAA+D;QAC/D,+DAA+D;QAC/D,gDAAgD;QAChD,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;QAClB,IAAI,CAAC,cAAc,GAAG,GAAG,CAAA;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,qBAAqB;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,CAAA;QAC9B,oBAAoB;QACpB,IAAI,CAAC,cAAc,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA;QACxD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAA;QACtC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QACvB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS,CAAC;YACf,KAAK,MAAM,CAAC;YACZ,KAAK,cAAc,CAAC;YACpB,KAAK,iBAAiB,CAAC;YACvB,KAAK,aAAa,CAAC;YACnB,KAAK,WAAW,CAAC;YACjB,KAAK,MAAM,CAAC;YACZ,KAAK,gBAAgB,CAAC;YACtB,KAAK,YAAY;gBACf,MAAK;YAEP,KAAK,yBAAyB,CAAC;YAC/B,KAAK,qBAAqB,CAAC;YAC3B,KAAK,gBAAgB,CAAC;YACtB,KAAK,sBAAsB,CAAC;YAC5B,KAAK,gBAAgB,CAAC;YACtB,KAAK,mBAAmB;gBACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;gBAChB,MAAK;YAEP,6DAA6D;YAC7D,sDAAsD;YACtD;gBACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACtB,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;QACvD,CAAC;QACD,oBAAoB;QAEpB,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAW,CAAA;QACvD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QACvB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAA;QAChC,CAAC;QACD,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAA;QACrB,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAA;QACrB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAA;QACvB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QACzB,qBAAqB;QACrB,IAAI,CAAC,QAAQ;YACX,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACf,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACvC,CAAC,CAAC,SAAS,CAAA;QACb,oBAAoB;QACpB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QAEzB,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QACjB,CAAC;QACD,IAAI,GAAG,EAAE,CAAC;YACR,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAY;QAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAA;QAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;QAC9D,CAAC;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;QACrB,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAA;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAA;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,QAAQ,CAAC,CAAA;QAC7C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,CAAC,IAAI,QAAQ,EAAE,CAAC;YAClB,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC1B,CAAC;QAED,eAAe;QACf,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACzC,CAAC;IAED,MAAM,CAAC,EAAO,EAAE,MAAe,KAAK;QAClC,IAAI,EAAE,CAAC,IAAI;YAAE,EAAE,CAAC,IAAI,GAAG,oBAAoB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,EAAE,CAAC,QAAQ;YAAE,EAAE,CAAC,QAAQ,GAAG,oBAAoB,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAA;QAChE,MAAM,CAAC,MAAM,CACX,IAAI,EACJ,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;YACnC,0DAA0D;YAC1D,4DAA4D;YAC5D,qCAAqC;YACrC,OAAO,CAAC,CACN,CAAC,KAAK,IAAI;gBACV,CAAC,KAAK,SAAS;gBACf,CAAC,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,CACtB,CAAA;QACH,CAAC,CAAC,CACH,CACF,CAAA;IACH,CAAC;CACF", "sourcesContent": ["import { Minipass } from 'minipass'\nimport { <PERSON><PERSON> } from './header.js'\nimport { normalizeWindowsPath } from './normalize-windows-path.js'\nimport { Pax } from './pax.js'\nimport { EntryTypeName } from './types.js'\n\nexport class ReadEntry extends Minipass<<PERSON><PERSON><PERSON>, Buffer> {\n  extended?: Pax\n  globalExtended?: Pax\n  header: Header\n  startBlockSize: number\n  blockRemain: number\n  remain: number\n  type: EntryTypeName\n  meta: boolean = false\n  ignore: boolean = false\n  path: string\n  mode?: number\n  uid?: number\n  gid?: number\n  uname?: string\n  gname?: string\n  size: number = 0\n  mtime?: Date\n  atime?: Date\n  ctime?: Date\n  linkpath?: string\n\n  dev?: number\n  ino?: number\n  nlink?: number\n  invalid: boolean = false\n  absolute?: string\n  unsupported: boolean = false\n\n  constructor(header: Header, ex?: Pax, gex?: Pax) {\n    super({})\n    // read entries always start life paused.  this is to avoid the\n    // situation where Minipass's auto-ending empty streams results\n    // in an entry ending before we're ready for it.\n    this.pause()\n    this.extended = ex\n    this.globalExtended = gex\n    this.header = header\n    /* c8 ignore start */\n    this.remain = header.size ?? 0\n    /* c8 ignore stop */\n    this.startBlockSize = 512 * Math.ceil(this.remain / 512)\n    this.blockRemain = this.startBlockSize\n    this.type = header.type\n    switch (this.type) {\n      case 'File':\n      case 'OldFile':\n      case 'Link':\n      case 'SymbolicLink':\n      case 'CharacterDevice':\n      case 'BlockDevice':\n      case 'Directory':\n      case 'FIFO':\n      case 'ContiguousFile':\n      case 'GNUDumpDir':\n        break\n\n      case 'NextFileHasLongLinkpath':\n      case 'NextFileHasLongPath':\n      case 'OldGnuLongPath':\n      case 'GlobalExtendedHeader':\n      case 'ExtendedHeader':\n      case 'OldExtendedHeader':\n        this.meta = true\n        break\n\n      // NOTE: gnutar and bsdtar treat unrecognized types as 'File'\n      // it may be worth doing the same, but with a warning.\n      default:\n        this.ignore = true\n    }\n\n    /* c8 ignore start */\n    if (!header.path) {\n      throw new Error('no path provided for tar.ReadEntry')\n    }\n    /* c8 ignore stop */\n\n    this.path = normalizeWindowsPath(header.path) as string\n    this.mode = header.mode\n    if (this.mode) {\n      this.mode = this.mode & 0o7777\n    }\n    this.uid = header.uid\n    this.gid = header.gid\n    this.uname = header.uname\n    this.gname = header.gname\n    this.size = this.remain\n    this.mtime = header.mtime\n    this.atime = header.atime\n    this.ctime = header.ctime\n    /* c8 ignore start */\n    this.linkpath =\n      header.linkpath ?\n        normalizeWindowsPath(header.linkpath)\n      : undefined\n    /* c8 ignore stop */\n    this.uname = header.uname\n    this.gname = header.gname\n\n    if (ex) {\n      this.#slurp(ex)\n    }\n    if (gex) {\n      this.#slurp(gex, true)\n    }\n  }\n\n  write(data: Buffer) {\n    const writeLen = data.length\n    if (writeLen > this.blockRemain) {\n      throw new Error('writing more to entry than is appropriate')\n    }\n\n    const r = this.remain\n    const br = this.blockRemain\n    this.remain = Math.max(0, r - writeLen)\n    this.blockRemain = Math.max(0, br - writeLen)\n    if (this.ignore) {\n      return true\n    }\n\n    if (r >= writeLen) {\n      return super.write(data)\n    }\n\n    // r < writeLen\n    return super.write(data.subarray(0, r))\n  }\n\n  #slurp(ex: Pax, gex: boolean = false) {\n    if (ex.path) ex.path = normalizeWindowsPath(ex.path)\n    if (ex.linkpath) ex.linkpath = normalizeWindowsPath(ex.linkpath)\n    Object.assign(\n      this,\n      Object.fromEntries(\n        Object.entries(ex).filter(([k, v]) => {\n          // we slurp in everything except for the path attribute in\n          // a global extended header, because that's weird. Also, any\n          // null/undefined values are ignored.\n          return !(\n            v === null ||\n            v === undefined ||\n            (k === 'path' && gex)\n          )\n        }),\n      ),\n    )\n  }\n}\n"]}